#!/usr/bin/env python3
"""
DyFlow v3.3 簡化 WebSocket 服務器
直接推送真實數據到前端
"""

import asyncio
import json
import websockets
import sys
import os
from pathlib import Path
from typing import Set, Dict, Any
import structlog
import aiohttp
from datetime import datetime

# 配置日誌
logger = structlog.get_logger(__name__)

class SimpleWebSocketServer:
    """簡化的 WebSocket 服務器"""
    
    def __init__(self):
        self.clients: Set[websockets.WebSocketServerProtocol] = set()
        self.running = False
        
        # API 端點
        self.endpoints = {
            "pancake_subgraph": "https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ",
            "meteora_dlmm_api": "https://dlmm-api.meteora.ag",
        }
        
        # 緩存
        self.last_data = {
            "bsc_pools": [],
            "sol_pools": [],
            "timestamp": None
        }
        
    async def register_client(self, websocket):
        """註冊新客戶端"""
        self.clients.add(websocket)
        logger.info("client_connected", client_count=len(self.clients))
        
        # 發送初始數據
        await self.send_initial_data(websocket)
        
    async def unregister_client(self, websocket):
        """註銷客戶端"""
        self.clients.discard(websocket)
        logger.info("client_disconnected", client_count=len(self.clients))
        
    async def send_initial_data(self, websocket):
        """發送初始數據給新連接的客戶端"""
        try:
            # 發送連接確認
            await websocket.send(json.dumps({
                "type": "connection_established",
                "data": {
                    "message": "Connected to DyFlow v3.3 Real Data Server",
                    "timestamp": datetime.now().timestamp()
                }
            }))
            
            # 發送當前池子數據
            if self.last_data["bsc_pools"] or self.last_data["sol_pools"]:
                await websocket.send(json.dumps({
                    "type": "initial_pool_data",
                    "data": {
                        "bsc_pools": self.last_data["bsc_pools"][:20],
                        "sol_pools": self.last_data["sol_pools"][:20],
                        "timestamp": datetime.now().timestamp()
                    }
                }))
                
        except Exception as e:
            logger.error("send_initial_data_failed", error=str(e))
    
    async def broadcast_to_all(self, message: Dict[str, Any]):
        """廣播消息給所有客戶端"""
        if not self.clients:
            return
            
        message_json = json.dumps(message)
        disconnected_clients = set()
        
        for client in self.clients:
            try:
                await client.send(message_json)
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(client)
            except Exception as e:
                logger.error("broadcast_failed", error=str(e))
                disconnected_clients.add(client)
        
        # 清理斷開的連接
        for client in disconnected_clients:
            self.clients.discard(client)
    
    async def handle_client(self, websocket, path):
        """處理客戶端連接"""
        await self.register_client(websocket)
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.handle_client_message(websocket, data)
                except json.JSONDecodeError:
                    logger.error("invalid_json_received", message=message)
        except websockets.exceptions.ConnectionClosed:
            pass
        finally:
            await self.unregister_client(websocket)
    
    async def handle_client_message(self, websocket, data: Dict[str, Any]):
        """處理客戶端消息"""
        message_type = data.get("type")
        
        if message_type == "ping":
            await websocket.send(json.dumps({
                "type": "pong",
                "data": {"timestamp": datetime.now().timestamp()}
            }))
        elif message_type == "request_pool_data":
            await self.send_pool_data_update()
    
    async def fetch_bsc_pools(self) -> list:
        """獲取 BSC 池子數據"""
        try:
            query = """
            {
              pools(first: 50, orderBy: totalValueLockedUSD, orderDirection: desc) {
                id
                token0 {
                  id
                  symbol
                  name
                  decimals
                }
                token1 {
                  id
                  symbol
                  name
                  decimals
                }
                feeTier
                totalValueLockedUSD
                volumeUSD
                feesUSD
                txCount
                createdAtTimestamp
              }
            }
            """
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.endpoints["pancake_subgraph"],
                    json={"query": query},
                    headers={
                        "Authorization": "Bearer 9731921233db132a98c2325878e6c153",
                        "Content-Type": "application/json"
                    },
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        pools = data.get("data", {}).get("pools", [])
                        
                        # 處理池子數據
                        processed_pools = []
                        for pool in pools:
                            if pool.get("token0") and pool.get("token1"):
                                token0 = pool["token0"]
                                token1 = pool["token1"]
                                
                                tvl_usd = float(pool.get("totalValueLockedUSD", 0))
                                volume_24h = float(pool.get("volumeUSD", 0))
                                fees_24h = float(pool.get("feesUSD", 0))
                                
                                fee_apr = (fees_24h * 365 / tvl_usd * 100) if tvl_usd > 0 else 0
                                
                                processed_pool = {
                                    "id": pool["id"],
                                    "chain": "bsc",
                                    "pair": f"{token0['symbol']}/{token1['symbol']}",
                                    "tvl_usd": tvl_usd,
                                    "volume_24h": volume_24h,
                                    "fees_24h": fees_24h,
                                    "fee_apr": fee_apr,
                                    "total_apr": fee_apr,
                                    "risk_level": "high" if fee_apr > 100 else "medium" if fee_apr > 50 else "low",
                                    "strategy_type": "SPOT_BALANCED",
                                    "last_update": datetime.now().isoformat()
                                }
                                processed_pools.append(processed_pool)
                        
                        return processed_pools
                        
        except Exception as e:
            logger.error("bsc_pools_fetch_error", error=str(e))
            return []
    
    async def fetch_sol_pools(self) -> list:
        """獲取 SOL 池子數據"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.endpoints['meteora_dlmm_api']}/pair/all",
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        pools = data if isinstance(data, list) else []
                        
                        # 處理池子數據
                        processed_pools = []
                        for pool in pools[:30]:  # 只取前 30 個
                            try:
                                name = pool.get("name", "")
                                if "-" in name:
                                    token_x_symbol = name.split("-")[0]
                                    token_y_symbol = name.split("-")[1]
                                else:
                                    continue
                                
                                tvl_usd = float(pool.get("liquidity", 0))
                                volume_24h = float(pool.get("trade_volume_24h", 0))
                                fees_24h = float(pool.get("fees_24h", 0))
                                
                                if tvl_usd < 10000:  # 過濾小池子
                                    continue
                                
                                fee_apr = (fees_24h * 365 / tvl_usd * 100) if tvl_usd > 0 else 0
                                
                                processed_pool = {
                                    "id": pool.get("address", ""),
                                    "chain": "solana",
                                    "pair": f"{token_x_symbol}/{token_y_symbol}",
                                    "tvl_usd": tvl_usd,
                                    "volume_24h": volume_24h,
                                    "fees_24h": fees_24h,
                                    "fee_apr": fee_apr,
                                    "total_apr": fee_apr,
                                    "risk_level": "high" if fee_apr > 200 else "medium" if fee_apr > 100 else "low",
                                    "strategy_type": "SPOT_IMBALANCED_DAMM",
                                    "last_update": datetime.now().isoformat()
                                }
                                processed_pools.append(processed_pool)
                                
                            except Exception as e:
                                continue
                        
                        return processed_pools
                        
        except Exception as e:
            logger.error("sol_pools_fetch_error", error=str(e))
            return []
    
    async def start_data_update_loop(self):
        """啟動數據更新循環"""
        logger.info("data_update_loop_starting")
        
        while self.running:
            try:
                # 並行獲取數據
                bsc_task = asyncio.create_task(self.fetch_bsc_pools())
                sol_task = asyncio.create_task(self.fetch_sol_pools())
                
                bsc_pools, sol_pools = await asyncio.gather(bsc_task, sol_task)
                
                # 更新緩存
                self.last_data["bsc_pools"] = bsc_pools
                self.last_data["sol_pools"] = sol_pools
                self.last_data["timestamp"] = datetime.now().isoformat()
                
                # 廣播更新
                await self.broadcast_to_all({
                    "type": "pool_data_update",
                    "data": {
                        "bsc_pools": bsc_pools[:20],
                        "sol_pools": sol_pools[:20],
                        "bsc_count": len(bsc_pools),
                        "sol_count": len(sol_pools),
                        "timestamp": datetime.now().timestamp()
                    }
                })
                
                logger.info("pool_data_updated", 
                           bsc_pools=len(bsc_pools),
                           sol_pools=len(sol_pools),
                           clients=len(self.clients))
                
                # 等待 30 秒
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error("data_update_loop_error", error=str(e))
                await asyncio.sleep(10)
    
    async def start_server(self, host="localhost", port=8001):
        """啟動 WebSocket 服務器"""
        self.running = True
        
        # 啟動數據更新循環
        data_task = asyncio.create_task(self.start_data_update_loop())
        
        # 啟動 WebSocket 服務器
        logger.info("websocket_server_starting", host=host, port=port)
        
        async with websockets.serve(self.handle_client, host, port):
            logger.info("websocket_server_started", host=host, port=port)
            print(f"🌐 WebSocket 服務器已啟動: ws://{host}:{port}")
            print("📊 正在推送真實的 PancakeSwap V3 和 Meteora DLMM v2 數據...")
            
            # 保持服務器運行
            await data_task

async def main():
    """主函數"""
    print("🚀 DyFlow v3.3 簡化 WebSocket 服務器啟動中...")
    
    server = SimpleWebSocketServer()
    
    try:
        await server.start_server()
    except KeyboardInterrupt:
        print("\n⏹️  收到停止信號")
        server.running = False
    except Exception as e:
        print(f"❌ 服務器錯誤: {e}")
        return 1
    
    print("👋 WebSocket 服務器已停止")
    return 0

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
