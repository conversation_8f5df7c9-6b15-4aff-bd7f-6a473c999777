/**
 * 系統概覽卡片 - KPI + 控制按鈕
 * 解決痛點①：KPI 與控制混雜
 */

import React from 'react'
import { Card, CardContent } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { 
  Play, 
  Pause, 
  Square, 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  Shield,
  Activity
} from 'lucide-react'
import { useSystemOverview, useTradingMode, useWorkflowStatus } from '../../store/useAgno'
import { startWorkflow, stopWorkflow, setTradingMode } from '../../lib/wsHub'

export const SystemOverview: React.FC = () => {
  const overview = useSystemOverview()
  const tradingMode = useTradingMode()
  const { running, currentPhase } = useWorkflowStatus()

  const handleStartWorkflow = () => {
    startWorkflow()
    setTradingMode('active')
  }

  const handleStopWorkflow = () => {
    stopWorkflow()
    setTradingMode('paused')
  }

  const handleToggleMode = () => {
    const nextMode = tradingMode === 'paused' ? 'active' : 
                    tradingMode === 'active' ? 'exit_only' : 'paused'
    setTradingMode(nextMode)
  }

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `$${(value / 1000000).toFixed(1)}M`
    if (value >= 1000) return `$${(value / 1000).toFixed(1)}K`
    return `$${value.toFixed(2)}`
  }

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? '+' : ''
    return `${sign}${value.toFixed(2)}%`
  }

  const getTradingModeColor = (mode: string) => {
    switch (mode) {
      case 'active': return 'bg-emerald-500'
      case 'exit_only': return 'bg-amber-500'
      case 'paused': return 'bg-gray-500'
      default: return 'bg-gray-500'
    }
  }

  const getTradingModeText = (mode: string) => {
    switch (mode) {
      case 'active': return '主動交易'
      case 'exit_only': return '僅退出'
      case 'paused': return '暫停'
      default: return '未知'
    }
  }

  const getRiskScoreColor = (score: number) => {
    if (score >= 80) return 'text-emerald-500'
    if (score >= 60) return 'text-amber-500'
    return 'text-rose-500'
  }

  return (
    <Card className="w-full bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 rounded-full bg-emerald-500 animate-pulse"></div>
            <h2 className="text-2xl font-bold text-gray-800">DyFlow v3.3 系統概覽</h2>
            <Badge 
              className={`${getTradingModeColor(tradingMode)} text-white`}
            >
              {getTradingModeText(tradingMode)}
            </Badge>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleToggleMode}
              className="text-sm"
            >
              切換模式
            </Button>
            
            {!running ? (
              <Button
                onClick={handleStartWorkflow}
                className="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-2"
              >
                <Play className="w-4 h-4 mr-2" />
                啟動 DyFlow Agent
              </Button>
            ) : (
              <Button
                onClick={handleStopWorkflow}
                variant="destructive"
                className="px-6 py-2"
              >
                <Square className="w-4 h-4 mr-2" />
                停止工作流程
              </Button>
            )}
          </div>
        </div>

        {/* KPI 指標區 */}
        <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
          {/* NAV */}
          <div className="bg-white rounded-lg p-4 shadow-sm border">
            <div className="flex items-center justify-between mb-2">
              <DollarSign className="w-5 h-5 text-blue-500" />
              <span className="text-xs text-gray-500">NAV</span>
            </div>
            <div className="text-xl font-bold text-gray-800">
              {formatCurrency(overview.nav)}
            </div>
          </div>

          {/* 24h 變化 */}
          <div className="bg-white rounded-lg p-4 shadow-sm border">
            <div className="flex items-center justify-between mb-2">
              {overview.nav24hPct >= 0 ? (
                <TrendingUp className="w-5 h-5 text-emerald-500" />
              ) : (
                <TrendingDown className="w-5 h-5 text-rose-500" />
              )}
              <span className="text-xs text-gray-500">24h 變化</span>
            </div>
            <div className={`text-xl font-bold ${overview.nav24hPct >= 0 ? 'text-emerald-600' : 'text-rose-600'}`}>
              {formatPercentage(overview.nav24hPct)}
            </div>
            <div className="text-xs text-gray-500">
              {formatCurrency(overview.nav24hChange)}
            </div>
          </div>

          {/* 24h 手續費 */}
          <div className="bg-white rounded-lg p-4 shadow-sm border">
            <div className="flex items-center justify-between mb-2">
              <DollarSign className="w-5 h-5 text-amber-500" />
              <span className="text-xs text-gray-500">24h 手續費</span>
            </div>
            <div className="text-xl font-bold text-gray-800">
              {formatCurrency(overview.fees24h)}
            </div>
          </div>

          {/* 風控分數 */}
          <div className="bg-white rounded-lg p-4 shadow-sm border">
            <div className="flex items-center justify-between mb-2">
              <Shield className="w-5 h-5 text-purple-500" />
              <span className="text-xs text-gray-500">風控分數</span>
            </div>
            <div className={`text-xl font-bold ${getRiskScoreColor(overview.riskScore)}`}>
              {overview.riskScore.toFixed(0)}
            </div>
          </div>

          {/* 持倉數量 */}
          <div className="bg-white rounded-lg p-4 shadow-sm border">
            <div className="flex items-center justify-between mb-2">
              <Activity className="w-5 h-5 text-indigo-500" />
              <span className="text-xs text-gray-500">持倉</span>
            </div>
            <div className="text-xl font-bold text-gray-800">
              {overview.activePositions}/{overview.totalPositions}
            </div>
          </div>

          {/* 當前階段 */}
          <div className="bg-white rounded-lg p-4 shadow-sm border">
            <div className="flex items-center justify-between mb-2">
              <div className={`w-5 h-5 rounded-full ${running ? 'bg-emerald-500 animate-pulse' : 'bg-gray-300'}`}></div>
              <span className="text-xs text-gray-500">階段</span>
            </div>
            <div className="text-xl font-bold text-gray-800">
              {currentPhase}/8
            </div>
            <div className="text-xs text-gray-500">
              {running ? '執行中' : '待機'}
            </div>
          </div>
        </div>

        {/* 工作流程狀態條 */}
        {running && (
          <div className="mt-4 bg-white rounded-lg p-3 shadow-sm border">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">工作流程進度</span>
              <span className="text-sm text-gray-500">{Math.round((currentPhase / 8) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-blue-500 to-emerald-500 h-2 rounded-full transition-all duration-500"
                style={{ width: `${(currentPhase / 8) * 100}%` }}
              ></div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
