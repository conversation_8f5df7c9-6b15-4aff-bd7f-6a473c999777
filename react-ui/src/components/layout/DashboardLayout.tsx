/**
 * DyFlow v3.3 Dashboard 佈局
 * 新版資訊架構：解決 5 個痛點的重新佈局
 */

import React, { useEffect } from 'react'
import { ToggleMode } from '../common/ToggleMode'
import { SystemOverview } from '../kpi/SystemOverview'
import { HotPoolTable } from '../pools/HotPoolTable'
import { PositionList } from '../lp/PositionList'
import { FlowTimeline } from '../agents/FlowTimeline'
import { RiskPanel } from '../risk/RiskPanel'
import { InfraStatus } from '../health/InfraStatus'
import { useConnectionStatus } from '../../store/useAgno'

export const DashboardLayout: React.FC = () => {
  const connectionStatus = useConnectionStatus()

  // 初始化 WebSocket 連接在 wsHub 中自動處理
  useEffect(() => {
    // WebSocket 連接已在 wsHub 中自動建立
    console.log('🎯 DashboardLayout mounted, WebSocket connection status:', connectionStatus)
  }, [connectionStatus])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 頂欄：連線狀態 / TradingMode Toggle */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-gray-800">DyFlow v3.3</h1>
            <span className="text-sm text-gray-500">24/7 自動化多 Agent LP 策略系統</span>
          </div>
          
          <ToggleMode />
        </div>
      </header>

      {/* 主要內容區 */}
      <main className="p-6 space-y-6">
        {/* ① 系統概覽卡 (上方橫跨) */}
        <section>
          <SystemOverview />
        </section>

        {/* ② 三欄資訊區 */}
        <section className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 池子熱度榜 */}
          <div className="lg:col-span-1">
            <HotPoolTable />
          </div>

          {/* LP 持倉列表 */}
          <div className="lg:col-span-1">
            <PositionList />
          </div>

          {/* Agent 流程 */}
          <div className="lg:col-span-1">
            <FlowTimeline />
          </div>
        </section>

        {/* ③ 風險監控 + 系統健康 (兩卡並排) */}
        <section className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 風險監控 */}
          <div>
            <RiskPanel />
          </div>

          {/* 系統健康 */}
          <div>
            <InfraStatus />
          </div>
        </section>
      </main>

      {/* 連接狀態指示器 */}
      {connectionStatus !== 'connected' && (
        <div className="fixed bottom-4 right-4 z-50">
          <div className={`rounded-lg px-4 py-2 text-white text-sm shadow-lg ${
            connectionStatus === 'connecting' ? 'bg-amber-500' :
            connectionStatus === 'disconnected' ? 'bg-gray-500' :
            connectionStatus === 'error' ? 'bg-rose-500' :
            connectionStatus === 'failed' ? 'bg-rose-600' : 'bg-gray-500'
          }`}>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                connectionStatus === 'connecting' ? 'bg-white animate-pulse' : 'bg-white'
              }`}></div>
              <span>
                {connectionStatus === 'connecting' ? '正在連接...' :
                 connectionStatus === 'disconnected' ? '連接已斷開' :
                 connectionStatus === 'error' ? '連接錯誤' :
                 connectionStatus === 'failed' ? '連接失敗' : '連接異常'}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
