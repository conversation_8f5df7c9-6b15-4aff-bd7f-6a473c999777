/**
 * 熱門池子表格
 * MarketIntelAgent 掃描結果展示
 */

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { 
  Search, 
  TrendingUp, 
  ExternalLink,
  Filter,
  RefreshCw,
  Target
} from 'lucide-react'
import { useAgnoStore } from '../../store/useAgno'

type ChainFilter = 'all' | 'bsc' | 'solana'
type SortField = 'tvl' | 'apr' | 'fees24h' | 'volatility'

export const HotPoolTable: React.FC = () => {
  const hotPools = useAgnoStore(state => state.hotPools)
  const [chainFilter, setChainFilter] = useState<ChainFilter>('all')
  const [sortField, setSortField] = useState<SortField>('apr')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('desc')
    }
  }

  const filteredAndSortedPools = hotPools
    .filter(pool => {
      if (chainFilter === 'all') return true
      return pool.chain === chainFilter
    })
    .sort((a, b) => {
      let aValue: number, bValue: number
      
      switch (sortField) {
        case 'tvl':
          aValue = a.tvl_usd || 0
          bValue = b.tvl_usd || 0
          break
        case 'apr':
          aValue = a.apr || 0
          bValue = b.apr || 0
          break
        case 'fees24h':
          aValue = a.fees_24h || 0
          bValue = b.fees_24h || 0
          break
        case 'volatility':
          aValue = a.volatility || 0
          bValue = b.volatility || 0
          break
        default:
          return 0
      }
      
      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue
    })

  const formatCurrency = (value: number) => {
    if (value >= 1000000) return `$${(value / 1000000).toFixed(1)}M`
    if (value >= 1000) return `$${(value / 1000).toFixed(1)}K`
    return `$${value.toFixed(2)}`
  }

  const getChainColor = (chain: string) => {
    switch (chain) {
      case 'bsc': return 'bg-amber-500'
      case 'solana': return 'bg-purple-500'
      default: return 'bg-gray-500'
    }
  }

  const getChainText = (chain: string) => {
    switch (chain) {
      case 'bsc': return 'BSC'
      case 'solana': return 'SOL'
      default: return chain.toUpperCase()
    }
  }

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'text-emerald-600 bg-emerald-50'
      case 'medium': return 'text-amber-600 bg-amber-50'
      case 'high': return 'text-rose-600 bg-rose-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getRiskText = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return '低風險'
      case 'medium': return '中風險'
      case 'high': return '高風險'
      default: return '未知'
    }
  }

  const getStrategyColor = (strategy: string) => {
    switch (strategy) {
      case 'SPOT_BALANCED': return 'bg-blue-500'
      case 'CURVE_BALANCED': return 'bg-green-500'
      case 'BID_ASK_BALANCED': return 'bg-purple-500'
      case 'SPOT_IMBALANCED_DAMM': return 'bg-orange-500'
      default: return 'bg-gray-500'
    }
  }

  const getStrategyText = (strategy: string) => {
    switch (strategy) {
      case 'SPOT_BALANCED': return '對稱'
      case 'CURVE_BALANCED': return '曲線'
      case 'BID_ASK_BALANCED': return '價差'
      case 'SPOT_IMBALANCED_DAMM': return '單邊'
      default: return '未知'
    }
  }

  const bscPools = hotPools.filter(p => p.chain === 'bsc').length
  const solanaPools = hotPools.filter(p => p.chain === 'solana').length

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Search className="w-5 h-5 text-green-500" />
            <span>池子熱度榜</span>
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            <Button size="sm" variant="outline" className="text-xs">
              <RefreshCw className="w-3 h-3 mr-1" />
              刷新
            </Button>
          </div>
        </div>
        
        {/* 統計信息 */}
        <div className="flex items-center space-x-4 text-sm text-gray-600">
          <span>總計: {hotPools.length} 個池子</span>
          <span>BSC: {bscPools}</span>
          <span>Solana: {solanaPools}</span>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {/* 篩選控制 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-400" />
            <div className="flex space-x-1">
              {(['all', 'bsc', 'solana'] as ChainFilter[]).map(chain => (
                <Button
                  key={chain}
                  size="sm"
                  variant={chainFilter === chain ? "default" : "ghost"}
                  onClick={() => setChainFilter(chain)}
                  className="text-xs px-2 py-1"
                >
                  {chain === 'all' ? '全部' : getChainText(chain)}
                </Button>
              ))}
            </div>
          </div>
          
          <div className="flex items-center space-x-1">
            {(['tvl', 'apr', 'fees24h'] as SortField[]).map(field => (
              <Button
                key={field}
                size="sm"
                variant={sortField === field ? "default" : "ghost"}
                onClick={() => handleSort(field)}
                className="text-xs px-2 py-1"
              >
                {field === 'tvl' ? 'TVL' :
                 field === 'apr' ? 'APR' :
                 field === 'fees24h' ? '手續費' : field}
              </Button>
            ))}
          </div>
        </div>

        {/* 池子列表 */}
        <div className="space-y-2 max-h-80 overflow-y-auto">
          {filteredAndSortedPools.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Search className="w-8 h-8 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">暫無池子數據</p>
            </div>
          ) : (
            filteredAndSortedPools.map((pool, index) => (
              <div 
                key={pool.id || index}
                className="bg-white rounded-lg border p-3 hover:shadow-sm transition-shadow"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-800">{pool.pair}</span>
                    <Badge className={`${getChainColor(pool.chain)} text-white text-xs`}>
                      {getChainText(pool.chain)}
                    </Badge>
                    {pool.strategy_recommendation && (
                      <Badge 
                        className={`${getStrategyColor(pool.strategy_recommendation)} text-white text-xs`}
                      >
                        {getStrategyText(pool.strategy_recommendation)}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Badge className={getRiskColor(pool.risk_level)}>
                      {getRiskText(pool.risk_level)}
                    </Badge>
                    <Button size="sm" variant="ghost" className="text-xs p-1">
                      <ExternalLink className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
                
                <div className="grid grid-cols-4 gap-3 text-sm">
                  <div>
                    <div className="text-xs text-gray-500">TVL</div>
                    <div className="font-medium text-gray-800">
                      {formatCurrency(pool.tvl_usd || 0)}
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-xs text-gray-500">APR</div>
                    <div className="font-medium text-emerald-600 flex items-center">
                      <TrendingUp className="w-3 h-3 mr-1" />
                      {(pool.apr || 0).toFixed(1)}%
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-xs text-gray-500">24h 手續費</div>
                    <div className="font-medium text-amber-600">
                      {formatCurrency(pool.fees_24h || 0)}
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-xs text-gray-500">波動率</div>
                    <div className="font-medium text-purple-600">
                      {((pool.volatility || 0) * 100).toFixed(1)}%
                    </div>
                  </div>
                </div>
                
                {pool.fee_tvl_ratio && (
                  <div className="mt-2 pt-2 border-t border-gray-100">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-500">Fee/TVL 比率:</span>
                      <span className="font-medium text-gray-700">
                        {(pool.fee_tvl_ratio * 100).toFixed(2)}%
                      </span>
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>

        {/* 掃描狀態 */}
        <div className="pt-3 border-t border-gray-100">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse"></div>
              <span className="text-gray-600">MarketIntelAgent 掃描中</span>
            </div>
            <span className="text-xs text-gray-500">
              上次更新: {new Date().toLocaleTimeString()}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
