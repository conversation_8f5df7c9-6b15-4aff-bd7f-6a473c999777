/**
 * DyFlow v3.3 WebSocket Hub
 * 連接 Agno Workflow API → dispatch 到 Zustand store
 * 替代輪詢 REST，實現純事件驅動
 */

import { useAgnoStore } from '../store/useAgno'

export interface WebSocketMessage {
  type: string
  data: any
  timestamp: string
}

// DyFlow v3.3 事件格式
export interface PoolEvent {
  pool_id: string
  chain: 'bsc' | 'sol'
  tvl: number
  fee_tvl_pct: number
  sigma: number
  spread: number
}

export interface LPPlanEvent {
  plan_id: string
  pool_id: string
  strategy: 'SPOT_BALANCED' | 'CURVE_BALANCED' | 'BID_ASK_BALANCED' | 'SPOT_IMBALANCED_DAMM'
  k: number
  notional_usd: number
  risk_profile: {
    il_cut: number
    sigma_cut: number
    var_cut: number
    holding_window: number
    exit_asset: string
  }
}

export interface ExitRequestEvent {
  position_id: string
  exit_asset: string
  reason: 'il_net_breach' | 'var_breach' | 'sigma_breach' | 'holding_window_expired'
}

export interface TxEvent {
  position_id: string
  status: 'open' | 'closed'
  tx_hash?: string
  exit_asset?: string
  qty?: number
}

export interface AgentFlowState {
  id: string
  name: string
  phase: number
  status: 'idle' | 'running' | 'completed' | 'failed'
  startedAt?: string
  completedAt?: string
  duration?: number
}

export interface PositionData {
  id: string
  pool: string
  chain: 'bsc' | 'solana'
  strategy: 'SPOT_BALANCED' | 'CURVE_BALANCED' | 'BID_ASK_BALANCED' | 'SPOT_IMBALANCED_DAMM'
  liquidityUsd: number
  pnlUsd: number
  pnlPct: number
  ilUsd: number
  ilPct: number
  apr: number
  feeApr: number
  fees24hUsd: number
  status: 'active' | 'exiting' | 'closed'
  exitAsset?: string
  exitAmount?: number
  holdingTime: number
  countdown: number
}

export interface RiskSummary {
  ilNet: number
  var95: number
  maxDrawdown: number
  riskScore: number
  healthScore: number
}

export interface InfraHealth {
  rpcBsc: { status: 'connected' | 'disconnected' | 'slow', latency: number }
  rpcSolana: { status: 'connected' | 'disconnected' | 'slow', latency: number }
  subgraphPancake: { status: 'connected' | 'disconnected' | 'slow', latency: number }
  apiMeteora: { status: 'connected' | 'disconnected' | 'slow', latency: number }
  dbSupabase: { status: 'connected' | 'disconnected' | 'slow', latency: number }
}

class WSHub {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectTimeout: NodeJS.Timeout | null = null
  private isConnecting = false

  constructor() {
    this.connect()
  }

  private connect() {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return
    }

    this.isConnecting = true
    const wsUrl = process.env.NODE_ENV === 'development'
      ? 'ws://localhost:8082/ws'
      : `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws`

    console.log('🔌 WSHub connecting to:', wsUrl)

    try {
      this.ws = new WebSocket(wsUrl)
      this.setupEventHandlers()
    } catch (error) {
      console.error('❌ WSHub connection failed:', error)
      this.isConnecting = false
      this.scheduleReconnect()
    }
  }

  private setupEventHandlers() {
    if (!this.ws) return

    this.ws.onopen = () => {
      console.log('✅ WSHub connected')
      this.isConnecting = false
      this.reconnectAttempts = 0
      useAgnoStore.getState().setConnectionStatus('connected')
    }

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data)
        this.handleMessage(message)
      } catch (error) {
        console.error('❌ WSHub message parse error:', error)
      }
    }

    this.ws.onclose = (event) => {
      console.log('🔌 WSHub disconnected:', event.code, event.reason)
      this.isConnecting = false
      useAgnoStore.getState().setConnectionStatus('disconnected')
      this.scheduleReconnect()
    }

    this.ws.onerror = (error) => {
      console.error('❌ WSHub error:', error)
      this.isConnecting = false
      useAgnoStore.getState().setConnectionStatus('error')
    }
  }

  private handleMessage(message: WebSocketMessage) {
    const store = useAgnoStore.getState()

    switch (message.type) {
      case 'initial_status':
      case 'status_update':
        this.handleStatusUpdate(message.data)
        break

      case 'workflow_started':
        store.setWorkflowRunning(true)
        console.log('🚀 Workflow started:', message.data.message)
        break

      case 'workflow_completed':
        store.setWorkflowRunning(false)
        console.log('✅ Workflow completed:', message.data.message)
        break

      case 'phase_started':
        this.handlePhaseUpdate(message.data, 'running')
        break

      case 'phase_completed':
        this.handlePhaseUpdate(message.data, message.data.success ? 'completed' : 'failed')
        break

      // DyFlow v3.3 核心事件
      case 'globalsDiff':
        store.setTradingMode(message.data.trading_mode)
        console.log('⚙️ Trading mode changed:', message.data.trading_mode)
        break

      case 'bus.pool':
        store.addPool(message.data as PoolEvent)
        console.log('🏊 Pool event:', message.data.pool_id)
        break

      case 'bus.lpplan':
        store.addLPPlan(message.data as LPPlanEvent)
        console.log('📋 LP Plan approved:', message.data.plan_id)
        break

      case 'bus.tx':
        this.handleTxEvent(message.data as TxEvent)
        break

      case 'ExitRequest':
        this.handleExitRequest(message.data as ExitRequestEvent)
        break

      case 'task_state_changed':
        this.handleAgentStateChange(message.data)
        break

      case 'lp_update':
        store.updatePositions(message.data.positions || [])
        break

      case 'risk_update':
        store.updateRiskSummary(message.data)
        break

      case 'health_update':
        store.updateInfraHealth(message.data)
        break

      case 'emergency_exit':
        console.log('🚨 Emergency exit triggered:', message.data.message)
        break

      // DyFlow v3.3 真實數據事件
      case 'connection_established':
        console.log('🌐 Connected to real data server:', message.data.message)
        break

      case 'initial_pool_data':
        this.handleRealPoolData(message.data)
        break

      case 'pool_data_update':
        this.handleRealPoolData(message.data)
        break

      case 'system_status_update':
        this.handleSystemStatusUpdate(message.data)
        break

      case 'trading_mode_updated':
        this.handleTradingModeUpdate(message.data)
        break

      case 'command_response':
        this.handleCommandResponse(message.data)
        break

      case 'agent_status_update':
        this.handleAgentStatusUpdate(message.data)
        break

      default:
        console.log('❓ Unknown message type:', message.type)
    }
  }

  private handleStatusUpdate(data: any) {
    const store = useAgnoStore.getState()
    
    if (data.trading_mode) {
      store.setTradingMode(data.trading_mode)
    }
    
    if (data.current_phase !== undefined) {
      store.setCurrentPhase(data.current_phase)
    }
    
    if (data.agents_status) {
      this.updateAgentFlow(data.agents_status)
    }
    
    if (data.phase_results) {
      this.updatePhaseResults(data.phase_results)
    }
  }

  private handlePhaseUpdate(data: any, status: string) {
    const store = useAgnoStore.getState()
    const agentFlow = store.agentFlow.map(agent => 
      agent.phase === data.phase_id 
        ? { ...agent, status: status as any, startedAt: data.started_at, completedAt: data.completed_at }
        : agent
    )
    store.setAgentFlow(agentFlow)
  }

  private updateAgentFlow(agentsStatus: any) {
    const agentFlow: AgentFlowState[] = Object.entries(agentsStatus).map(([name, status]: [string, any]) => ({
      id: name.toLowerCase(),
      name,
      phase: status.phase,
      status: status.status === 'initialized' ? 'idle' : status.status,
      startedAt: status.last_activity
    }))
    
    useAgnoStore.getState().setAgentFlow(agentFlow)
  }

  private updatePhaseResults(phaseResults: any[]) {
    // 更新階段結果到 store
    useAgnoStore.getState().setPhaseResults(phaseResults)
  }

  private handleTxEvent(txEvent: TxEvent) {
    const store = useAgnoStore.getState()

    if (txEvent.status === 'open') {
      console.log('📈 Position opened:', txEvent.position_id)
      // 更新持倉狀態為 active
      store.updatePosition(txEvent.position_id, {
        status: 'active',
        tx_hash: txEvent.tx_hash
      })
    } else if (txEvent.status === 'closed') {
      console.log('📉 Position closed:', txEvent.position_id, 'Exit asset:', txEvent.exit_asset, txEvent.qty)
      // 更新持倉狀態為 closed，顯示退出資產
      store.updatePosition(txEvent.position_id, {
        status: 'closed',
        exit_asset: txEvent.exit_asset,
        exit_amount: txEvent.qty
      })
    }
  }

  private handleExitRequest(exitRequest: ExitRequestEvent) {
    const store = useAgnoStore.getState()

    console.log('🚨 Exit request:', exitRequest.position_id, 'Reason:', exitRequest.reason)

    // 更新持倉狀態為 exiting
    store.updatePosition(exitRequest.position_id, {
      status: 'exiting',
      exit_reason: exitRequest.reason
    })
  }

  private handleAgentStateChange(data: any) {
    const store = useAgnoStore.getState()

    // 更新 Agent 狀態
    const agentFlow = store.agentFlow.map(agent =>
      agent.name === data.agent_name
        ? { ...agent, status: data.status, last_activity: data.timestamp }
        : agent
    )

    store.setAgentFlow(agentFlow)
    console.log('🤖 Agent state changed:', data.agent_name, '→', data.status)
  }

  private scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('❌ WSHub max reconnect attempts reached')
      useAgnoStore.getState().setConnectionStatus('failed')
      return
    }

    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000)
    console.log(`🔄 WSHub reconnecting in ${delay/1000}s (attempt ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts})`)
    
    this.reconnectTimeout = setTimeout(() => {
      this.reconnectAttempts++
      this.connect()
    }, delay)
  }

  public sendMessage(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
      console.log('📤 WSHub sent:', message.type)
    } else {
      console.warn('⚠️ WSHub not connected, message not sent:', message.type)
    }
  }

  private handleRealPoolData(data: any) {
    const store = useAgnoStore.getState()

    console.log('📊 Real pool data received:', {
      bsc_pools: data.bsc_pools?.length || 0,
      sol_pools: data.sol_pools?.length || 0,
      bsc_count: data.bsc_count || 0,
      sol_count: data.sol_count || 0
    })

    // 合併 BSC 和 SOL 池子數據
    const allPools = [
      ...(data.bsc_pools || []),
      ...(data.sol_pools || [])
    ]

    // 更新 store 中的池子數據
    store.setHotPools(allPools)

    console.log('✅ Pool data updated in store:', allPools.length, 'pools')
  }

  private handleSystemStatusUpdate(data: any) {
    const store = useAgnoStore.getState()

    console.log('📈 System status update:', {
      total_pools: data.total_pools,
      bsc_pools: data.bsc_pools_count,
      sol_pools: data.sol_pools_count,
      data_source: data.data_source
    })

    // 可以在這裡更新系統狀態相關的數據
    // 例如更新全局統計信息
  }

  private handleTradingModeUpdate(data: any) {
    const store = useAgnoStore.getState()

    console.log('🔄 Trading mode updated:', data.mode)

    // 更新交易模式到 store
    store.setTradingMode(data.mode)
  }

  private handleCommandResponse(data: any) {
    console.log('✅ Command response:', {
      command: data.command,
      status: data.status,
      error: data.error
    })

    if (data.status === 'error') {
      console.error('❌ Command failed:', data.command, data.error)
    }
  }

  private handleAgentStatusUpdate(data: any) {
    const store = useAgnoStore.getState()

    console.log('🤖 Agent status update:', {
      agent: data.agent,
      phase: data.phase,
      status: data.status,
      message: data.message
    })

    // 映射 agent 名稱到 ID
    const agentNameToId: Record<string, string> = {
      'SupervisorAgent': 'supervisor',
      'HealthGuardAgent': 'health',
      'MarketIntelAgent': 'market',
      'PortfolioManagerAgent': 'portfolio',
      'StrategyAgent': 'strategy',
      'ExecutionAgent': 'execution',
      'RiskSentinelAgent': 'risk'
    }

    const agentId = agentNameToId[data.agent] || data.agent.toLowerCase()

    // 更新 agent 狀態到 store
    store.updateAgentStatus(agentId, {
      phase: data.phase,
      status: data.status,
      message: data.message,
      timestamp: new Date().toISOString()
    })
  }

  public disconnect() {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
    }
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }
}

// 單例實例
export const wsHub = new WSHub()

// 便捷方法
export const sendAgentCommand = (command: string, payload?: any) => {
  wsHub.sendMessage({
    type: 'agent_command',
    command,
    payload: payload || {}
  })
}

export const startWorkflow = () => sendAgentCommand('start_workflow')
export const stopWorkflow = () => sendAgentCommand('stop_workflow')
export const setTradingMode = (mode: 'paused' | 'exit_only' | 'active') => 
  sendAgentCommand('set_trading_mode', { mode })
export const emergencyExit = () => sendAgentCommand('emergency_exit')
