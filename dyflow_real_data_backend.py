#!/usr/bin/env python3
"""
DyFlow 真实数据后端 - 连接真实的DEX APIs
提供真实的池子数据和交易功能
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
import asyncio
import json
import aiohttp
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional
import uvicorn
from supabase import create_client, Client
import sys
import os

# 添加 src 到路径以導入 portfolio API
sys.path.insert(0, 'src')

# 設置統一日誌
from src.utils.logger import setup_logging, get_logger, log_api_call, log_pool_data

# 初始化日誌
setup_logging(level='INFO', use_emoji=True, use_colors=True)
logger = get_logger(__name__)

app = FastAPI(title="DyFlow Real Data Dashboard")

# 添加 CORS 中間件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 導入並包含 portfolio API
try:
    from api.portfolio_api import router as portfolio_router
    app.include_router(portfolio_router, prefix="/api", tags=["portfolio"])
    logger.info("Portfolio API 路由已加載", component="portfolio_api")
except ImportError as e:
    logger.warning("無法加載 Portfolio API", error=str(e), component="portfolio_api")

# 導入 Workflow 執行器
try:
    from workflow.dyflow_workflow_executor import workflow_executor
    logger.info("Workflow 執行器已加載", component="workflow")
except ImportError as e:
    logger.warning("無法加載 Workflow 執行器", error=str(e), component="workflow")
    workflow_executor = None

# Supabase配置
SUPABASE_URL = "https://ikxobiwfymtxhumpntw.supabase.co"
SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlreG9iaXdoZnltdHhodW1wbnR3Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczOTg5MDY1MiwiZXhwIjoyMDU1NDY2NjUyfQ.P4vR_QS5GXPU1zh25qb9pgWmg82QEJoymiVO8-7-REE"

supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)

# WebSocket连接管理
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                self.active_connections.remove(connection)

manager = ConnectionManager()

class StrategyAnalyzer:
    """策略分析器 - 根據 PRD 要求實現策略矩陣"""

    @staticmethod
    def analyze_strategy(pool_data: Dict) -> str:
        """
        根據池子數據分析推薦策略
        策略矩陣：
        - 低波動雙邊：SPOT_BALANCED
        - 高波動單邊 + 動態費：SPOT_IMBALANCED_DAMM
        - 曲線分布：CURVE_BALANCED
        - 價差策略：BID_ASK_BALANCED
        """
        try:
            tvl_usd = pool_data.get('tvl_usd', 0)
            apr = pool_data.get('apr', 0)
            fees_24h = pool_data.get('fees_24h', 0)
            chain = pool_data.get('chain', '')

            # 計算波動率指標
            sigma = StrategyAnalyzer.calculate_volatility(pool_data)

            # 計算 ATR (Average True Range) 代理指標
            atr = StrategyAnalyzer.calculate_atr_proxy(pool_data)

            # 計算 Spread 指標
            spread = StrategyAnalyzer.calculate_spread(pool_data)

            # 策略矩陣邏輯
            if sigma < 0.02:  # 低波動 (< 2%)
                if tvl_usd > 1000000:  # 高TVL
                    return "SPOT_BALANCED"  # 低波動雙邊
                else:
                    return "CURVE_BALANCED"  # 曲線分布
            elif sigma > 0.05:  # 高波動 (> 5%)
                if chain == 'solana' and apr > 100:  # Solana 高APR
                    return "SPOT_IMBALANCED_DAMM"  # 高波動單邊 + 動態費
                else:
                    return "BID_ASK_BALANCED"  # 價差策略
            else:  # 中等波動
                if spread > 0.001:  # 高價差
                    return "BID_ASK_BALANCED"
                else:
                    return "SPOT_BALANCED"

        except Exception as e:
            return "SPOT_BALANCED"  # 默認策略

    @staticmethod
    def calculate_volatility(pool_data: Dict) -> float:
        """計算波動率 (sigma)"""
        try:
            # 基於 APR 和 TVL 的波動率估算
            apr = pool_data.get('apr', 0)
            tvl_usd = pool_data.get('tvl_usd', 0)

            if tvl_usd == 0:
                return 0.1  # 默認高波動

            # 簡化的波動率計算：APR 越高，波動率越高
            if apr > 200:
                return 0.08  # 8% 高波動
            elif apr > 100:
                return 0.05  # 5% 中高波動
            elif apr > 50:
                return 0.03  # 3% 中等波動
            else:
                return 0.015  # 1.5% 低波動

        except Exception:
            return 0.03  # 默認中等波動

    @staticmethod
    def calculate_atr_proxy(pool_data: Dict) -> float:
        """計算 ATR 代理指標"""
        try:
            # 基於 24h 交易量和 TVL 的比率
            volume_24h = pool_data.get('volume_24h', 0)
            tvl_usd = pool_data.get('tvl_usd', 0)

            if tvl_usd == 0:
                return 0.01

            return min(volume_24h / tvl_usd, 1.0)  # 限制在 100% 以內

        except Exception:
            return 0.01

    @staticmethod
    def calculate_spread(pool_data: Dict) -> float:
        """計算 Spread 指標"""
        try:
            # 基於手續費率的 Spread 估算
            fee_tier = pool_data.get('fee_tier', 0.0025)  # 默認 0.25%
            return fee_tier / 100  # 轉換為小數

        except Exception:
            return 0.0003  # 默認 0.03%

class RealDataProvider:
    """真实数据提供者"""

    def __init__(self):
        self.session = None
        self.last_prices = {}
        self.last_pools_update = {}
        self.strategy_analyzer = StrategyAnalyzer()
        
    async def init_session(self):
        """初始化HTTP会话"""
        if not self.session:
            self.session = aiohttp.ClientSession()
    
    async def close_session(self):
        """关闭HTTP会话"""
        if self.session:
            await self.session.close()
    
    async def get_token_prices(self):
        """获取代币价格 - 多重备用方案"""
        try:
            await self.init_session()

            # 方案1: CoinGecko API (免费版本，有限制)
            try:
                url = "https://api.coingecko.com/api/v3/simple/price"
                params = {
                    "ids": "binancecoin,solana,usd-coin,tether",
                    "vs_currencies": "usd",
                    "include_24hr_change": "true"
                }

                timeout = aiohttp.ClientTimeout(total=5)
                async with self.session.get(url, params=params, timeout=timeout) as response:
                    if response.status == 200:
                        data = await response.json()

                        prices = {
                            "BNB": data.get("binancecoin", {}).get("usd", 680),
                            "SOL": data.get("solana", {}).get("usd", 140),
                            "USDC": data.get("usd-coin", {}).get("usd", 1.0),
                            "USDT": data.get("tether", {}).get("usd", 1.0),
                            "USD1": 0.998,
                            "changes": {
                                "BNB": data.get("binancecoin", {}).get("usd_24h_change", 0),
                                "SOL": data.get("solana", {}).get("usd_24h_change", 0),
                                "USDC": data.get("usd-coin", {}).get("usd_24h_change", 0),
                                "USDT": data.get("tether", {}).get("usd_24h_change", 0)
                            }
                        }

                        self.last_prices = prices
                        log_api_call(logger, "GET", url, status=response.status,
                                   message="成功获取CoinGecko价格数据")
                        return prices
                    elif response.status == 429:
                        log_api_call(logger, "GET", url, status=response.status,
                                   message="CoinGecko API限制")
                        return {}
                    else:
                        log_api_call(logger, "GET", url, status=response.status,
                                   message="CoinGecko API異常狀態")
                        return {}

            except asyncio.TimeoutError:
                logger.warning("CoinGecko API超时", api="coingecko", action="get_prices")
                return self.get_simulated_prices()
            except Exception as e:
                logger.error("CoinGecko API错误", api="coingecko", error=str(e))
                return self.get_simulated_prices()

        except Exception as e:
            logger.error("获取价格数据失败", error=str(e))
            return {}


    
    def get_fallback_prices(self):
        """备用价格数据"""
        return {
            "BNB": 680,
            "SOL": 140,
            "USDC": 1.0,
            "USDT": 1.0,
            "USD1": 0.998,
            "changes": {"BNB": 0, "SOL": 0, "USDC": 0, "USDT": 0}
        }
    
    async def get_pancakeswap_pools(self):
        """获取BSC池子数据 - 多重备用方案"""
        try:
            await self.init_session()

            # 方案1: 直接使用PancakeSwap V3 Subgraph (跳过V2 API)
            logger.info("開始獲取PancakeSwap V3 Subgraph數據", api="pancakeswap_v3")
            try:
                subgraph_url = "https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ"
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": "Bearer 9731921233db132a98c2325878e6c153"
                }

                # 获取更多池子数据 - 移除限制
                query = """
                {
                  pools(first: 100, orderBy: totalValueLockedUSD, orderDirection: desc, where: {totalValueLockedUSD_gt: "10000"}) {
                    id
                    token0 {
                      symbol
                      name
                    }
                    token1 {
                      symbol
                      name
                    }
                    feeTier
                    totalValueLockedUSD
                    volumeUSD
                    token0Price
                    token1Price
                    poolDayData(first: 7, orderBy: date, orderDirection: desc) {
                      date
                      volumeUSD
                      feesUSD
                      high
                      low
                      close
                    }
                  }
                }
                """

                timeout = aiohttp.ClientTimeout(total=30)
                async with self.session.post(subgraph_url,
                                           json={"query": query},
                                           headers=headers,
                                           timeout=timeout) as response:
                    if response.status == 200:
                        graph_data = await response.json()
                        pools_count = len(graph_data.get('data', {}).get('pools', []))
                        logger.info("PancakeSwap Subgraph響應成功",
                                  api="pancakeswap_v3", pools_count=pools_count)

                        pools = self.parse_subgraph_data(graph_data)
                        if pools:
                            logger.info("成功获取PancakeSwap V3數據",
                                      api="pancakeswap_v3", pools_count=len(pools))
                            return pools
                        else:
                            logger.warning("PancakeSwap Subgraph解析後返回空數據",
                                         api="pancakeswap_v3")
                    else:
                        error_text = await response.text()
                        log_api_call(logger, "POST", subgraph_url, status=response.status,
                                   message="PancakeSwap Subgraph API錯誤", error=error_text)

            except Exception as e:
                logger.error("PancakeSwap Subgraph API錯誤", api="pancakeswap_v3", error=str(e))



        except Exception as e:
            logger.error("获取BSC数据失败", error=str(e), chain="bsc")

        # 如果所有API都失败，返回空列表而不是模拟数据
        logger.error("无法获取BSC真实数据", chain="bsc", action="get_pools")
        return []

    def parse_subgraph_data(self, data):
        """解析PancakeSwap V3 Subgraph数据"""
        try:
            pools = []
            pools_data = data.get("data", {}).get("pools", [])
            logger.info("開始解析PancakeSwap池子",
                       pools_count=len(pools_data), protocol="pancakeswap_v3")

            for i, pool_data in enumerate(pools_data):
                try:
                    if i < 3:  # 只显示前3个池子的详细信息
                        logger.debug("處理PancakeSwap池子詳情",
                                   pool_index=i, pool_data=pool_data)
                    # 提取池子信息
                    pool_id = pool_data.get("id", "")

                    # 代币信息
                    token0 = pool_data.get("token0", {})
                    token1 = pool_data.get("token1", {})

                    if not token0 or not token1:
                        continue

                    token0_symbol = token0.get("symbol", "")
                    token1_symbol = token1.get("symbol", "")
                    pair_name = f"{token0_symbol}/{token1_symbol}"

                    # TVL和交易量 - 使用真实Subgraph数据 (修复异常大数值)
                    tvl_usd_raw = float(pool_data.get("totalValueLockedUSD", 0))
                    volume_usd_raw = float(pool_data.get("volumeUSD", 0))
                    fees_usd = float(pool_data.get("feesUSD", 0))

                    # 修复异常大的数值 - 这些数值看起来是正确的USD，但是单位可能有问题
                    # 从日志看，2.07e+17 应该是 207,000,000,000,000,000 USD，这明显不对
                    # 可能是以某种小数位单位存储的，让我们尝试不同的转换
                    if tvl_usd_raw > 1e12:  # 如果TVL超过1万亿，可能需要转换
                        # 尝试除以1e12 (万亿) 而不是1e18
                        tvl_usd = tvl_usd_raw / 1e12
                        volume_usd = volume_usd_raw / 1e12
                    else:
                        tvl_usd = tvl_usd_raw
                        volume_usd = volume_usd_raw

                    # 正确的单位转换
                    tvl = tvl_usd / 1000000  # USD转换为百万美元 (M)
                    volume_24h = volume_usd / 1000  # USD转换为千美元 (K)

                    if i < 3:  # 显示前3个池子的转换信息
                        logger.debug("TVL轉換詳情", pool_index=i,
                                   raw_tvl=f"{tvl_usd_raw:.2e}",
                                   converted_tvl=f"${tvl:.3f}M")

                    # 手续费率 - 修复计算
                    fee_tier = float(pool_data.get("feeTier", 2500)) / 10000  # 转换为百分比 (例如2500 = 0.25%)

                    # 数据验证 - 过滤异常数据
                    if tvl_usd > 1000000000:  # TVL超过10亿美元，可能是错误数据
                        continue
                    if volume_usd > 10000000000:  # 交易量超过100亿美元，可能是错误数据
                        continue

                    # 计算APR - 修复计算逻辑
                    if tvl_usd > 10000 and volume_usd > 0:  # 至少1万美元TVL
                        # 基于交易量和手续费率计算APR
                        # fee_tier是基点(bps)，例如500 = 0.05%
                        fee_percentage = fee_tier / 10000  # 转换为小数
                        daily_fees = volume_usd * fee_percentage  # 日手续费收入
                        apr = (daily_fees * 365 / tvl_usd) * 100
                        apr = min(max(apr, 0), 10000)  # 限制在0-10000%之间（DeFi可以有很高APR）

                        if i < 3:  # 显示前3个池子的APR计算详情
                            logger.debug("APR計算詳情", pool_index=i,
                                       daily_fees=f"${daily_fees:.0f}",
                                       tvl=f"${tvl_usd:.0f}", apr=f"{apr:.1f}%")
                    else:
                        apr = 0

                    # 过滤低TVL池子 - 调整为合理的阈值
                    if tvl < 0.01:  # 至少0.01M TVL (10,000 USD)
                        logger.debug("過濾低TVL池子", pair=pair_name,
                                   tvl=f"{tvl:.3f}M", reason="TVL太低")
                        continue

                    # 过滤无效数据
                    if not token0_symbol or not token1_symbol:
                        logger.debug("過濾無效池子", reason="缺少代币符号")
                        continue

                    log_pool_data(logger, pair_name, tvl * 1000000, apr, "bsc")

                    # 风险评估
                    if tvl > 10:
                        risk_level = "低"
                    elif tvl > 2:
                        risk_level = "中"
                    else:
                        risk_level = "高"

                    # 投资建议
                    if apr > 50:
                        recommendation = "BUY"
                    elif apr > 20:
                        recommendation = "HOLD"
                    else:
                        recommendation = "AVOID"

                    # 構建池子數據
                    pool_data = {
                        "pair": pair_name,
                        "protocol": "PancakeSwap V3",
                        "address": pool_id,
                        "chain": "bsc",  # 添加链标识
                        "tvl_usd": round(tvl * 1000000, 0),  # 转换回USD
                        "volume_24h": round(volume_24h * 1000, 0),  # 转换回USD
                        "fees_24h": round(volume_24h * 1000 * fee_tier / 100, 0),  # 计算24h手续费
                        "apr": round(apr, 1),
                        "risk_level": risk_level,
                        "fee_tier": fee_tier,
                        "recommendation": recommendation
                    }

                    # 添加策略分析
                    strategy = self.strategy_analyzer.analyze_strategy(pool_data)
                    pool_data["strategy_recommendation"] = strategy

                    # 添加波動率數據
                    sigma = self.strategy_analyzer.calculate_volatility(pool_data)
                    pool_data["volatility"] = sigma
                    pool_data["sigma"] = sigma

                    # 添加 Spread 數據
                    spread = self.strategy_analyzer.calculate_spread(pool_data)
                    pool_data["spread"] = spread

                    # 添加 Fee/TVL 比率
                    if pool_data["tvl_usd"] > 0:
                        pool_data["fee_tvl_ratio"] = pool_data["fees_24h"] / pool_data["tvl_usd"]
                    else:
                        pool_data["fee_tvl_ratio"] = 0

                    pools.append(pool_data)

                except Exception as e:
                    if i < 5:  # 只显示前5个错误
                        logger.warning("PancakeSwap池子解析錯誤",
                                     pool_index=i, error=str(e), pool_data=pool_data)
                    continue

            # 按TVL排序并返回所有符合条件的池子
            pools.sort(key=lambda x: x["tvl_usd"], reverse=True)
            logger.info("PancakeSwap池子解析完成",
                       pools_count=len(pools), protocol="pancakeswap_v3")
            return pools

        except Exception as e:
            logger.error("解析PancakeSwap Subgraph數據失敗",
                        protocol="pancakeswap_v3", error=str(e))
            return []

    def parse_pancakeswap_data(self, data):
        """解析PancakeSwap数据 - 修复数据显示问题"""
        try:
            pools = []
            target_pairs = ["BNB", "WBNB", "USDC", "USDT", "BUSD", "USD1"]

            for pair_id, pair_data in data.get("data", {}).items():
                try:
                    token0 = pair_data.get("token0", {})
                    token1 = pair_data.get("token1", {})

                    token0_symbol = token0.get("symbol", "")
                    token1_symbol = token1.get("symbol", "")

                    if any(target in [token0_symbol, token1_symbol] for target in target_pairs):
                        pair_name = f"{token0_symbol}/{token1_symbol}"

                        # 修复TVL计算 - 使用reserveUSD字段
                        reserve_usd = float(pair_data.get("reserveUSD", 0))
                        if reserve_usd > 0:
                            tvl = reserve_usd / 1000000  # 转换为百万美元
                        else:
                            # 备用计算方法
                            reserve0 = float(pair_data.get("reserve0", 0))
                            reserve1 = float(pair_data.get("reserve1", 0))
                            # 假设平均价格，简化计算
                            tvl = max(reserve0, reserve1) / 1000000

                        # 修复24h交易量计算
                        volume_usd = float(pair_data.get("volumeUSD", 0))
                        volume_24h = volume_usd / 1000  # 转换为千美元

                        # 修复APR计算 - 添加合理限制
                        if tvl > 0.1:  # 至少0.1M TVL
                            daily_fees = volume_24h * 0.0025  # 0.25% 手续费
                            apr = (daily_fees * 365 / (tvl * 1000)) * 100
                            # 限制APR在合理范围内
                            apr = min(max(apr, 0), 200)  # 0-200% 之间
                        else:
                            apr = 0

                        # 数据验证 - 过滤异常数据
                        if tvl > 1000 or apr > 200 or volume_24h > 100000:
                            continue

                        # 风险评估
                        risk_level = "低" if tvl > 5 else "中" if tvl > 1 else "高"

                        pools.append({
                            "pair": pair_name,
                            "protocol": "PancakeSwap V2",
                            "address": pair_id,
                            "tvl": round(tvl, 1),
                            "volume_24h": round(volume_24h, 0),
                            "apr": round(apr, 1),
                            "risk_level": risk_level,
                            "recommendation": "BUY" if apr > 50 else "HOLD" if apr > 20 else "AVOID"
                        })

                except Exception as e:
                    continue

            pools.sort(key=lambda x: x["apr"], reverse=True)
            logger.info("PancakeSwap池子篩選完成",
                       pools_count=len(pools), protocol="pancakeswap_v3")
            return pools  # 返回所有符合条件的池子

        except Exception as e:
            logger.error("解析PancakeSwap數據失敗",
                        protocol="pancakeswap_v3", error=str(e))
            return []
    
    async def get_solana_pools(self):
        """获取Solana DEX池子数据 - 使用真实Meteora API"""
        try:
            await self.init_session()

            # 使用真实的Meteora DAMM v2 API - 按TVL排序获取高质量池子
            meteora_urls = [
                "https://dammv2-api.meteora.ag/pools?order_by=tvl&order=desc&limit=50&offset=0",   # 按TVL降序，前50个
                "https://dammv2-api.meteora.ag/pools?order_by=apr&order=desc&limit=50&offset=0",   # 按APR降序，前50个
                "https://dammv2-api.meteora.ag/pools?order_by=volume24h&order=desc&limit=50&offset=0",  # 按24h交易量降序，前50个
            ]
            timeout = aiohttp.ClientTimeout(total=30)

            # 获取多页DAMM v2数据并合并
            all_pools = []
            successful_pages = 0

            for meteora_url in meteora_urls:
                try:
                    logger.info("嘗試Meteora API", url=meteora_url, api="meteora")
                    async with self.session.get(meteora_url, timeout=timeout) as response:
                        if response.status == 200:
                            data = await response.json()
                            data_length = len(data) if isinstance(data, (list, dict)) else 'N/A'
                            logger.info("Meteora API響應成功",
                                      api="meteora", data_type=type(data).__name__,
                                      data_length=data_length)

                            pools = self.parse_meteora_data(data)
                            if pools:
                                all_pools.extend(pools)
                                successful_pages += 1
                                logger.info("成功獲取Meteora頁面數據",
                                          page=successful_pages, pools_count=len(pools),
                                          api="meteora", url=meteora_url)
                            else:
                                logger.warning("Meteora API返回空數據",
                                             api="meteora", url=meteora_url)
                        else:
                            log_api_call(logger, "GET", meteora_url, status=response.status,
                                       message="Meteora API異常狀態")
                except Exception as e:
                    logger.error("Meteora API錯誤", api="meteora",
                               url=meteora_url, error=str(e))
                    continue

            # 如果获取到数据，合并并返回
            if all_pools:
                # 去重 (基于池子地址)
                unique_pools = {}
                for pool in all_pools:
                    address = pool.get("address", "")
                    if address and address not in unique_pools:
                        unique_pools[address] = pool

                final_pools = list(unique_pools.values())
                # 按APR排序
                final_pools.sort(key=lambda x: x["apr"], reverse=True)
                logger.info("合併Meteora數據完成",
                          pages_merged=successful_pages,
                          unique_pools=len(final_pools), api="meteora")
                return final_pools  # 返回所有符合条件的池子

        except asyncio.TimeoutError:
            logger.warning("Meteora API超時", api="meteora")
        except Exception as e:
            logger.error("Meteora API錯誤", api="meteora", error=str(e))

        # 如果API失败，返回空列表而不是模拟数据
        logger.error("無法獲取Solana真實數據", chain="solana", api="meteora")
        return []

    def parse_meteora_data(self, data):
        """解析Meteora DAMM v2 API数据"""
        try:
            # 定义安全转换函数
            def safe_float(value, default=0):
                try:
                    if isinstance(value, (int, float)):
                        return float(value)
                    elif isinstance(value, str):
                        return float(value) if value else default
                    else:
                        return default
                except (ValueError, TypeError):
                    return default

            pools = []
            # DAMM v2 API返回格式: {"status": 200, "data": [...]}
            pairs_data = data if isinstance(data, list) else data.get("data", [])
            total_pools = data.get("total", len(pairs_data)) if isinstance(data, dict) else len(pairs_data)
            logger.info("開始解析DAMM v2池子",
                       pools_to_parse=len(pairs_data), total_pools=total_pools,
                       protocol="meteora_damm_v2")

            for i, pair_data in enumerate(pairs_data):
                try:
                    # DAMM v2 池子信息提取
                    pool_address = pair_data.get("pool_address", "")
                    pool_name = pair_data.get("pool_name", "")

                    # 代币信息 - DAMM v2 字段名
                    token_a_symbol = pair_data.get("token_a_symbol", "")
                    token_b_symbol = pair_data.get("token_b_symbol", "")
                    token_a_mint = pair_data.get("token_a_mint", "")
                    token_b_mint = pair_data.get("token_b_mint", "")

                    # 构建池子名称
                    name = pool_name or f"{token_a_symbol}-{token_b_symbol}"

                    if i < 3:  # 只显示前3个池子的处理信息
                        logger.debug("處理DAMM v2池子詳情",
                                   pool_index=i, pool_name=name, protocol="meteora_damm_v2")

                    # DAMM v2 API 字段 - 使用正确的字段名
                    tvl_usd = safe_float(pair_data.get("tvl", 0))
                    volume_24h = safe_float(pair_data.get("volume24h", 0))
                    fees_24h = safe_float(pair_data.get("fee24h", 0))
                    apr = safe_float(pair_data.get("apr", 0))

                    # 代币金额 (用于验证)
                    token_a_amount = safe_float(pair_data.get("token_a_amount", 0))
                    token_b_amount = safe_float(pair_data.get("token_b_amount", 0))
                    token_a_amount_usd = safe_float(pair_data.get("token_a_amount_usd", 0))
                    token_b_amount_usd = safe_float(pair_data.get("token_b_amount_usd", 0))

                    # 如果API没有提供TVL，尝试从代币金额计算
                    if tvl_usd == 0 and (token_a_amount_usd > 0 or token_b_amount_usd > 0):
                        tvl_usd = token_a_amount_usd + token_b_amount_usd
                        if i < 3:
                            logger.debug("TVL計算詳情", pool_index=i,
                                       token_a_usd=f"${token_a_amount_usd:.0f}",
                                       token_b_usd=f"${token_b_amount_usd:.0f}",
                                       tvl=f"${tvl_usd:.0f}", protocol="meteora_damm_v2")

                    # 手续费相关
                    base_fee = safe_float(pair_data.get("base_fee", 0))
                    dynamic_fee = safe_float(pair_data.get("dynamic_fee", 0))
                    fee_rate = (base_fee + dynamic_fee) / 10000  # 转换为百分比

                    # APR处理 - DAMM v2 API 直接提供APR
                    if apr == 0 and tvl_usd > 0 and fees_24h > 0:
                        # 如果API没有提供APR，基于24h手续费收入计算年化收益
                        apr = (fees_24h * 365 / tvl_usd) * 100
                        apr = min(max(apr, 0), 10000)  # 限制在0-10000%之间

                        if i < 3:  # 显示前3个池子的APR计算详情
                            logger.debug("DAMM v2 APR計算詳情", pool_index=i,
                                       fees_24h=f"${fees_24h:.0f}", tvl=f"${tvl_usd:.0f}",
                                       apr=f"{apr:.1f}%", protocol="meteora_damm_v2")
                    elif apr == 0 and tvl_usd > 0 and volume_24h > 0:
                        # 备用计算：基于交易量和手续费率
                        daily_fees = volume_24h * fee_rate  # fee_rate已经是百分比
                        apr = (daily_fees * 365 / tvl_usd) * 100
                        apr = min(max(apr, 0), 10000)

                        if i < 3:  # 显示前3个池子的APR计算详情
                            logger.debug("DAMM v2 APR計算(備用)", pool_index=i,
                                       daily_fees=f"${daily_fees:.0f}", tvl=f"${tvl_usd:.0f}",
                                       apr=f"{apr:.1f}%", protocol="meteora_damm_v2")

                    # 确保APR在合理范围内
                    apr = min(max(apr, 0), 10000)

                    # DAMM v2 放宽过滤条件 - 先获取更多数据
                    # 1. TVL >= $1K (放宽从$10K到$1K)
                    if tvl_usd < 1000:  # 至少1K USD TVL
                        if i < 5:
                            logger.debug("過濾低TVL池子", pool_name=name,
                                       tvl=f"${tvl_usd:.0f}", threshold="$1K",
                                       protocol="meteora_damm_v2")
                        continue

                    # 2. 24h Fees > $1 (放宽从$5到$1)
                    if fees_24h <= 1:
                        if i < 5:
                            logger.debug("過濾低手續費池子", pool_name=name,
                                       fees_24h=f"${fees_24h:.2f}", threshold="$1",
                                       protocol="meteora_damm_v2")
                        continue

                    # 3. Fee/TVL >= 1.00% (放宽从5%到1%)
                    fee_tvl_ratio = (fees_24h / tvl_usd) * 100 if tvl_usd > 0 else 0
                    if fee_tvl_ratio < 1.0:
                        if i < 5:
                            logger.debug("過濾低Fee/TVL池子", pool_name=name,
                                       fee_tvl_ratio=f"{fee_tvl_ratio:.2f}%",
                                       threshold="1.00%", protocol="meteora_damm_v2")
                        continue

                    # 暂时跳过创建时间过滤，因为API可能没有这个字段
                    # created_at = pair_data.get("created_at_slot_timestamp", 0)

                    # 过滤异常高TVL池子 (可能是数据错误)
                    if tvl_usd > 1e11:  # 超过1000亿美元的池子很可能是数据错误
                        logger.warning("過濾異常高TVL池子", pool_name=name,
                                     tvl=f"${tvl_usd:.0f}", tvl_billions=f"${tvl_usd/1e9:.1f}B",
                                     reason="可能是數據錯誤", protocol="meteora_damm_v2")
                        continue

                    # 跳过没有名称的池子
                    if not name or name.strip() == "":
                        continue

                    # 只处理前10个池子的调试信息
                    if i < 10:
                        log_pool_data(logger, name, tvl_usd, apr, "solana")

                    # 风险评估
                    if tvl_usd > 1000000:  # 超过100万美元
                        risk_level = "低"
                    elif tvl_usd > 100000:  # 超过10万美元
                        risk_level = "中"
                    else:
                        risk_level = "高"

                    # 投资建议
                    if apr > 100:
                        recommendation = "BUY"
                    elif apr > 50:
                        recommendation = "HOLD"
                    else:
                        recommendation = "AVOID"

                    # 構建池子數據
                    pool_data = {
                        "pair": name,
                        "protocol": "Meteora DAMM v2",
                        "address": pool_address,
                        "chain": "solana",  # 添加链标识
                        "tvl_usd": round(tvl_usd, 0),  # 直接使用API提供的USD值
                        "volume_24h": round(volume_24h, 0),  # 24h交易量
                        "fees_24h": round(fees_24h, 0),  # 24h手续费
                        "apr": round(apr, 1),
                        "risk_level": risk_level,
                        "fee_tier": fee_rate,
                        "recommendation": recommendation
                    }

                    # 添加策略分析
                    strategy = self.strategy_analyzer.analyze_strategy(pool_data)
                    pool_data["strategy_recommendation"] = strategy

                    # 添加波動率數據
                    sigma = self.strategy_analyzer.calculate_volatility(pool_data)
                    pool_data["volatility"] = sigma
                    pool_data["sigma"] = sigma

                    # 添加 Spread 數據
                    spread = self.strategy_analyzer.calculate_spread(pool_data)
                    pool_data["spread"] = spread

                    # 添加 Fee/TVL 比率
                    if pool_data["tvl_usd"] > 0:
                        pool_data["fee_tvl_ratio"] = pool_data["fees_24h"] / pool_data["tvl_usd"]
                    else:
                        pool_data["fee_tvl_ratio"] = 0

                    pools.append(pool_data)

                except Exception as e:
                    if i < 5:  # 只显示前5个错误
                        logger.warning("DAMM v2池子解析錯誤", pool_index=i,
                                     error=str(e), protocol="meteora_damm_v2")
                    continue

            # 按APR排序并返回所有符合条件的池子
            pools.sort(key=lambda x: x["apr"], reverse=True)
            logger.info("DAMM v2池子解析完成",
                       pools_count=len(pools), protocol="meteora_damm_v2",
                       criteria="TVL>=$1K, Fee/TVL>=1%, 24h费用>$1")
            return pools  # 返回所有符合条件的池子

        except Exception as e:
            logger.error("解析DAMM v2數據失敗",
                        protocol="meteora_damm_v2", error=str(e))
            return []
    

    

    
    async def execute_trade(self, trade_data: Dict):
        """执行交易 (模拟)"""
        try:
            # 这里可以集成真实的交易执行逻辑
            # 例如连接到Web3钱包、DEX路由器等
            
            trade_result = {
                "success": True,
                "tx_hash": f"0x{hash(str(trade_data)) % (10**16):016x}",
                "timestamp": datetime.now().isoformat(),
                "trade_data": trade_data,
                "estimated_gas": "0.005 BNB" if trade_data.get("chain") == "BSC" else "0.001 SOL",
                "slippage": "0.5%",
                "status": "pending"
            }
            
            # 保存交易记录到Supabase
            await self.save_trade_to_db(trade_result)
            
            return trade_result
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def save_trade_to_db(self, trade_data: Dict):
        """保存交易到数据库"""
        try:
            supabase.table('trades').insert({
                'tx_hash': trade_data.get('tx_hash'),
                'trade_data': trade_data,
                'status': trade_data.get('status'),
                'created_at': datetime.now().isoformat()
            }).execute()
        except Exception as e:
            logger.error("保存交易失敗", error=str(e))

# 全局数据提供者
data_provider = RealDataProvider()

@app.get("/", response_class=HTMLResponse)
async def get_dashboard():
    """返回Dashboard HTML页面"""
    try:
        with open("dyflow_enhanced_dashboard.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(content="""
        <html>
        <head><title>DyFlow Real Data Dashboard</title></head>
        <body>
        <h1>🚀 DyFlow 真实数据Dashboard</h1>
        <p>HTML文件未找到，请先创建 dyflow_real_dashboard.html</p>
        <p>WebSocket连接: ws://localhost:8001/ws</p>
        <p>真实数据API: http://localhost:8001/api/real-data</p>
        </body>
        </html>
        """)

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点 - 3-5秒更新"""
    await manager.connect(websocket)
    try:
        while True:
            # 接收客户端消息
            try:
                data = await asyncio.wait_for(websocket.receive_text(), timeout=0.1)
                message_data = json.loads(data)
                
                if message_data["type"] == "chat_message":
                    response = await process_chat_message(message_data["message"])
                    await websocket.send_text(json.dumps(response))
                elif message_data["type"] == "execute_trade":
                    trade_result = await data_provider.execute_trade(message_data["trade_data"])
                    await websocket.send_text(json.dumps({
                        "type": "trade_result",
                        "result": trade_result
                    }))
                elif message_data["type"] == "agent_command":
                    # 處理 Agent 命令
                    command_result = await process_agent_command(message_data)
                    await websocket.send_text(json.dumps(command_result))
                    
            except asyncio.TimeoutError:
                pass
            except json.JSONDecodeError:
                pass
            
            # 获取真实数据
            prices = await data_provider.get_token_prices()
            bsc_pools = await data_provider.get_pancakeswap_pools()
            solana_pools = await data_provider.get_solana_pools()
            
            # 获取Agent状态数据
            agents_response = await get_agents_status()

            # 发送实时数据更新
            realtime_data = {
                "type": "real_data_update",
                "timestamp": datetime.now().isoformat(),
                "prices": prices,
                "bsc_pools": bsc_pools,
                "solana_pools": solana_pools,
                "agents_status": agents_response,  # 添加Agent状态
                "update_interval": "3-5秒",
                "data_source": "真实API"
            }
            
            await websocket.send_text(json.dumps(realtime_data))
            
            # 随机等待3-5秒
            import random
            wait_time = random.uniform(3, 5)
            await asyncio.sleep(wait_time)
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)

async def process_agent_command(message_data: dict):
    """處理 Agent 命令"""
    try:
        command = message_data.get("command")
        data = message_data.get("data", {})

        if command == "start_workflow":
            # 啟動工作流程
            if workflow_executor and not workflow_executor.is_running:
                # 模擬 8-phase 啟動序列
                phases = [
                    {"agent": "SupervisorAgent", "phase": 0, "status": "running", "message": "系統初始化中..."},
                    {"agent": "HealthGuardAgent", "phase": 1, "status": "running", "message": "健康檢查中..."},
                    {"agent": "MarketIntelAgent", "phase": 4, "status": "running", "message": "掃描池子中..."},
                    {"agent": "PortfolioManagerAgent", "phase": 5, "status": "running", "message": "計算資金配置..."},
                    {"agent": "StrategyAgent", "phase": 6, "status": "running", "message": "生成策略計劃..."},
                    {"agent": "ExecutionAgent", "phase": 7, "status": "running", "message": "準備執行交易..."},
                    {"agent": "RiskSentinelAgent", "phase": 8, "status": "running", "message": "啟動風險監控..."}
                ]

                # 發送 Agent 狀態更新
                for phase_info in phases:
                    await manager.broadcast({
                        "type": "agent_status_update",
                        "agent": phase_info["agent"],
                        "phase": phase_info["phase"],
                        "status": phase_info["status"],
                        "message": phase_info["message"],
                        "timestamp": datetime.now().isoformat()
                    })
                    await asyncio.sleep(0.5)  # 短暫延遲模擬啟動過程

                # 發送工作流程啟動成功消息
                await manager.broadcast({
                    "type": "workflow_started",
                    "message": "所有 Agent 已啟動，系統進入主動交易模式",
                    "timestamp": datetime.now().isoformat()
                })

                return {
                    "type": "command_response",
                    "command": "start_workflow",
                    "status": "success",
                    "message": "工作流程啟動成功",
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "type": "command_response",
                    "command": "start_workflow",
                    "status": "error",
                    "message": "工作流程已在運行或不可用",
                    "timestamp": datetime.now().isoformat()
                }

        elif command == "stop_workflow":
            # 停止工作流程
            await manager.broadcast({
                "type": "workflow_stopped",
                "message": "工作流程已停止",
                "timestamp": datetime.now().isoformat()
            })

            return {
                "type": "command_response",
                "command": "stop_workflow",
                "status": "success",
                "message": "工作流程停止成功",
                "timestamp": datetime.now().isoformat()
            }

        elif command == "set_trading_mode":
            # 設置交易模式
            mode = data.get("mode", "paused")

            await manager.broadcast({
                "type": "trading_mode_update",
                "mode": mode,
                "timestamp": datetime.now().isoformat()
            })

            return {
                "type": "command_response",
                "command": "set_trading_mode",
                "status": "success",
                "message": f"交易模式已設置為: {mode}",
                "data": {"mode": mode},
                "timestamp": datetime.now().isoformat()
            }

        else:
            return {
                "type": "command_response",
                "command": command,
                "status": "error",
                "message": f"未知命令: {command}",
                "timestamp": datetime.now().isoformat()
            }

    except Exception as e:
        return {
            "type": "command_response",
            "command": message_data.get("command", "unknown"),
            "status": "error",
            "message": f"命令處理錯誤: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }

async def process_chat_message(message: str):
    """处理聊天消息"""
    message_lower = message.lower()

    if "交易" in message_lower or "买入" in message_lower or "投资" in message_lower:
        return {
            "type": "chat_response",
            "timestamp": datetime.now().isoformat(),
            "user_message": message,
            "ai_response": "🤖 **交易助手**:\n\n我可以帮你执行以下操作:\n\n1. **分析池子** - 评估风险和收益\n2. **执行交易** - 自动买入LP位置\n3. **设置策略** - 配置自动化投资\n4. **风险管理** - 监控和调整持仓\n\n请告诉我你想投资哪个池子？"
        }
    elif "扫描" in message_lower:
        bsc_pools = await data_provider.get_pancakeswap_pools()
        solana_pools = await data_provider.get_solana_pools()

        response = "🔍 **真实池子扫描结果**:\n\n"
        response += "**🟡 BSC Top 3 (真实数据):**\n"
        for i, pool in enumerate(bsc_pools[:3], 1):
            response += f"{i}. {pool['pair']} - APR: {pool['apr']}%\n"
            response += f"   TVL: ${pool['tvl']}M | 协议: {pool['protocol']}\n\n"

        response += "**🟣 Solana Top 3:**\n"
        for i, pool in enumerate(solana_pools[:3], 1):
            response += f"{i}. {pool['pair']} - APR: {pool['apr']}%\n"
            response += f"   TVL: ${pool['tvl']}M | 协议: {pool['protocol']}\n\n"

        return {
            "type": "chat_response",
            "timestamp": datetime.now().isoformat(),
            "user_message": message,
            "ai_response": response
        }
    else:
        return {
            "type": "chat_response",
            "timestamp": datetime.now().isoformat(),
            "user_message": message,
            "ai_response": "🤖 我可以帮你:\n- 扫描真实池子数据\n- 执行LP投资交易\n- 分析风险和收益\n- 管理投资组合\n\n试试说：'扫描最佳池子' 或 '我想投资BNB/USDC'"
        }

@app.get("/api/real-data")
async def get_real_data():
    """获取真实数据的REST API"""
    prices = await data_provider.get_token_prices()
    bsc_pools = await data_provider.get_pancakeswap_pools()
    solana_pools = await data_provider.get_solana_pools()
    
    return {
        "timestamp": datetime.now().isoformat(),
        "prices": prices,
        "bsc_pools": bsc_pools,
        "solana_pools": solana_pools,
        "data_source": "真实API"
    }

@app.post("/api/execute-trade")
async def execute_trade_api(trade_data: dict):
    """执行交易API"""
    result = await data_provider.execute_trade(trade_data)
    return result

# Workflow API 端點
@app.get("/api/phases/status")
async def get_phases_status():
    """獲取階段狀態"""
    if workflow_executor:
        status = workflow_executor.get_status()
        return status
    else:
        # 備用數據
        return {
            "current_phase": 0,
            "phase_status": {},
            "total_phases": 9,
            "completed_phases": 0,
            "progress_percentage": 0,
            "is_running": False,
            "agents": {},
            "last_updated": datetime.now(timezone.utc).isoformat()
        }

@app.post("/api/workflow/start")
async def start_workflow():
    """啟動工作流程"""
    if not workflow_executor:
        return {"error": "Workflow executor not available"}

    if workflow_executor.is_running:
        return {"error": "Workflow is already running"}

    # 在背景啟動工作流程
    asyncio.create_task(workflow_executor.start_workflow())

    return {"message": "Workflow started", "status": "running"}

@app.post("/api/workflow/reset")
async def reset_workflow():
    """重置工作流程"""
    if not workflow_executor:
        return {"error": "Workflow executor not available"}

    await workflow_executor.reset_workflow()
    return {"message": "Workflow reset", "status": "reset"}

@app.get("/api/agents/status")
async def get_agents_status():
    """获取所有Agent状态"""
    try:
        # 模拟Agent状态数据
        agents_status = {
            "data_provider": {
                "name": "DataProviderAgent",
                "status": "active",
                "last_run": datetime.now(timezone.utc).isoformat(),
                "next_run": (datetime.now(timezone.utc) + timedelta(minutes=3)).isoformat(),
                "success_rate": 95.5,
                "total_runs": 1247,
                "errors": 12,
                "data": {
                    "bsc_pools_monitored": 20,
                    "solana_pools_monitored": 31,
                    "last_update": datetime.now(timezone.utc).isoformat()
                }
            },
            "planner": {
                "name": "PlannerAgnoAgent",
                "status": "active",
                "last_run": (datetime.now(timezone.utc) - timedelta(minutes=2)).isoformat(),
                "next_run": (datetime.now(timezone.utc) + timedelta(minutes=3)).isoformat(),
                "success_rate": 88.2,
                "total_runs": 892,
                "errors": 23,
                "data": {
                    "pools_analyzed": 51,
                    "recommendations": 8,
                    "high_apr_pools": 5
                }
            },
            "risk_sentinel": {
                "name": "RiskSentinelAgent",
                "status": "active",
                "last_run": (datetime.now(timezone.utc) - timedelta(minutes=1)).isoformat(),
                "next_run": (datetime.now(timezone.utc) + timedelta(minutes=1)).isoformat(),
                "success_rate": 99.1,
                "total_runs": 2156,
                "errors": 3,
                "data": {
                    "positions_monitored": 12,
                    "risk_alerts": 0,
                    "il_warnings": 1
                }
            },
            "portfolio": {
                "name": "PortfolioAgent",
                "status": "active",
                "last_run": (datetime.now(timezone.utc) - timedelta(minutes=8)).isoformat(),
                "next_run": (datetime.now(timezone.utc) + timedelta(minutes=7)).isoformat(),
                "success_rate": 92.7,
                "total_runs": 445,
                "errors": 8,
                "data": {
                    "total_value_usd": 125000,
                    "positions": 12,
                    "daily_pnl": 2450,
                    "apy": 156.8
                }
            }
        }

        return {
            "agents": agents_status,
            "summary": {
                "total_agents": len(agents_status),
                "active_agents": sum(1 for a in agents_status.values() if a["status"] == "active"),
                "total_runs": sum(a["total_runs"] for a in agents_status.values()),
                "total_errors": sum(a["errors"] for a in agents_status.values()),
                "avg_success_rate": sum(a["success_rate"] for a in agents_status.values()) / len(agents_status)
            },
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.error("Agent狀態API錯誤", error=str(e))
        return {"error": str(e)}

# 測試 StrategyAgent
@app.post("/api/test/strategy-agent")
async def test_strategy_agent():
    """測試 StrategyAgent 是否能正常工作"""
    try:
        logger.info("開始測試 StrategyAgent", test="strategy_agent")

        # 導入 StrategyAgent
        from src.agents.strategy_agent import StrategyAgent

        # 創建 StrategyAgent 實例
        config = {
            'model_provider': 'mock',
            'model_name': 'mock-model'
        }

        strategy_agent = StrategyAgent("StrategyAgent", config)
        logger.info("StrategyAgent 實例創建成功", test="strategy_agent")

        # 初始化
        init_result = await strategy_agent.initialize()
        logger.info("StrategyAgent 初始化完成",
                   test="strategy_agent", init_result=init_result)

        if not init_result:
            return {
                "status": "error",
                "message": "StrategyAgent 初始化失敗",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        # 生成策略計劃
        plans_result = await strategy_agent.generate_lp_plans()
        logger.info("StrategyAgent 策略生成完成",
                   test="strategy_agent", plans_count=len(plans_result.get('plans', [])))

        return {
            "status": "success",
            "message": "StrategyAgent 測試成功",
            "init_result": init_result,
            "plans_result": plans_result,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        logger.error("StrategyAgent 測試失敗", test="strategy_agent", error=str(e))
        import traceback
        traceback.print_exc()
        return {
            "status": "error",
            "message": f"StrategyAgent 測試失敗: {str(e)}",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

# 手動觸發 Phase 6
@app.post("/api/test/phase6")
async def test_phase6():
    """手動測試 Phase 6 策略生成"""
    try:
        logger.info("開始手動測試 Phase 6", test="phase6")

        if not workflow_executor:
            return {
                "status": "error",
                "message": "Workflow executor 不可用",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        # 手動執行 Phase 6
        result = await workflow_executor.execute_phase(6)
        logger.info("Phase 6 執行完成", test="phase6", result=result)

        return {
            "status": "success",
            "message": "Phase 6 測試完成",
            "result": result,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

    except Exception as e:
        logger.error("Phase 6 測試失敗", test="phase6", error=str(e))
        import traceback
        traceback.print_exc()
        return {
            "status": "error",
            "message": f"Phase 6 測試失敗: {str(e)}",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8082)
